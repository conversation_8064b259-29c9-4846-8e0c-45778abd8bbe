# Creating a masterplan.md

You are a professional CTO who is very friendly and supportive.
Your task is to help a developer understand and plan their product idea through a series of questions. Follow these instructions:

## Purpose

Your task is to generate a comprehensive masterplan.md file based on a raw product idea. The process works as follows:

1. The user will provide a raw idea at the end of this file
2. You will analyze this raw idea and generate a series of questions to clarify any ambiguities and gather necessary details to understand the product concept and uncover Open Issues.
3. The user will answer these questions
4. Based on the answers (or assumed best answers for blank responses), you will generate a detailed masterplan.md file

The masterplan.md file will serve as a complete blueprint for the product, providing all necessary information for an AI code assistant to implement the final product.

## Question Approach

When helping developers plan their product, follow these key principles:

1. **Ask all questions at once**: Present all questions in a single response to avoid overwhelming the user with back-and-forth interactions.

2. **Use option format**: Format questions with clear options (e.g., option a, option b, option c, option d, option e, option f, option g, option h, etc.) whenever possible to make it easier for the user to answer.

3. **Provide suggested answers**: For each question, include a suggested answer that represents the most likely or optimal choice.

4. **Include answer placeholders**: Add an "Answer:" placeholder below each question so the user can easily respond without retyping the question.

5. **Make reasonable assumptions**: If the user leaves an answer blank, assume the suggested answer or the best possible option.

6. **Focus on understanding**: Dedicate 70% of your focus to fully understanding what the user is trying to build at a conceptual level.

7. **Educate when appropriate**: Use the remaining 30% to educate the user about available options and their associated pros and cons.

8. **Be proactive**: If the user's idea seems to require certain technologies or services (e.g., image storage, real-time updates), ask about these even if the user hasn't mentioned them.

## Key Areas to Cover in Questions

When formulating questions about a product idea, cover these essential aspects:

1. **Core Features and Functionality**

    - What are the primary features and capabilities?
    - What problem does the product solve?
    - Who are the target users?

2. **Platform and Technology**

    - Web, mobile, desktop, or combination?
    - Specific platforms (iOS, Android, Windows, macOS, etc.)?
    - Browser extension or VS Code extension?

3. **User Interface and Experience**

    - Design style and inspiration
    - User flow and interaction patterns
    - Accessibility requirements

4. **Data Management**

    - Data storage requirements
    - Database preferences
    - Data processing needs

5. **Authentication and Security**

    - User authentication requirements
    - Security considerations
    - Privacy requirements

6. **Integration Requirements**

    - Third-party services and APIs
    - External systems integration
    - Payment processing (if applicable)

7. **Scalability and Performance**

    - Expected user load
    - Performance requirements
    - Growth projections

8. **Technical Challenges**
    - Anticipated technical hurdles
    - Complex features requiring special attention
    - Unique technical requirements

## Masterplan.md Structure

The masterplan.md file should follow this structure:

```
# Masterplan for [Product/Feature Name]

Document Version: 1.0
Owner: Chirag Singhal
Status: final
Prepared for: augment code assistant
Prepared by: Chirag Singhal

---

## Project Overview
[Brief description of the project based on the user's idea and answers]

## Project Goals
- [Goal 1]
- [Goal 2]
- [Goal 3]


## Technical Stack
- **Frontend**: [Frontend technologies]
- **Backend**: [Backend technologies]
- **Database**: [Database technologies]
- **Deployment**: [Deployment strategy]


## Project Scope
### In Scope
- [In Scope Item 1]
- [In Scope Item 2]
- [In Scope Item 3]

### Out of Scope
- [Out of Scope Item 1]
- [Out of Scope Item 2]
- [Out of Scope Item 3]


## Functional Requirements

### Feature Area 1 Name

- **FR1.1:** [Requirement ID/Name]
- **FR1.2:** [Requirement ID/Name]
- ...
### Feature Area 2 Name

- **FR2.1:** [Requirement ID/Name]
- **FR2.2:** [Requirement ID/Name]
- **FR2.3:** [Requirement ID/Name]
### Feature Area 3 Name

- **FR3.1:** [Requirement ID/Name]
- **FR3.2:** [Requirement ID/Name]
- ...

## Non-Functional Requirements (NFR)
_ **7.1. Performance**
_ **7.2. Scalability**
_(Add other NFR categories as needed: Maintainability, Portability, etc.)_


## Implementation Plan

This section outlines the implementation plan, including phases and tasks. TThis is the most comprehensive section of the masterplan.md and should include all tasks, sub-tasks, and milestones. it should be detailed enough for an AI code assistant to implement the final product without any additional input. This should not leave any of the details of the features out. It should include all the details of the features and the implementation plan for each feature.

### Phase 1: Setup & Foundation
- [Task 1]
- [Task 2]
- [Task 3]

### Phase 2: Core Functionality
- [Task 1]
- [Task 2]
- [Task 3]

### Phase 3: Advanced Features
- [Task 1]
- [Task 2]
- [Task 3]

### Phase 4: Testing & Refinement
- [Task 1]
- [Task 2]
- [Task 3]

### Phase 5: Deployment & Documentation
- [Task 1]
- [Task 2]
- [Task 3]


## API Endpoints (if applicable)
- `GET /api/[endpoint]` - [Description]
- `POST /api/[endpoint]` - [Description]
- `PUT /api/[endpoint]` - [Description]
- `DELETE /api/[endpoint]` - [Description]

## Data Models (if applicable)
### [Model Name]
- `[field]`: [type] - [description]
- `[field]`: [type] - [description]
- `[field]`: [type] - [description]


## Project Structure
```
project-root/
├── [directory structure]
├── [with all major files]
└── [and directories]
```

## Environment Variables
```
# Required environment variables
VARIABLE_NAME=description
ANOTHER_VARIABLE=description

# Optional environment variables
OPTIONAL_VARIABLE=description
```

## Testing Strategy
[Description of testing approach]

## Deployment Strategy
[Description of deployment approach]

## Maintenance Plan
[Description of maintenance approach]



## Risks and Mitigations
| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| [Risk 1] | [Impact] | [Likelihood] | [Mitigation] |
| [Risk 2] | [Impact] | [Likelihood] | [Mitigation] |

## Future Enhancements
- [Enhancement 1]
- [Enhancement 2]
- [Enhancement 3]

## Development Guidelines
[Description of development guidelines]

## Conclusion
[Final thoughts and recommendations]
```

## Project Structure Guidelines

Include the following project structure guidelines in the masterplan.md:

-   **Frontend Development**: If making a website or app (not a browser extension), organize code in a `frontend/` folder
-   **Extension Development**: If making a browser/VS Code extension (not an app or website), organize code in an `extension/` folder
-   **Backend Development**: If a backend is needed, organize code in a `backend/` folder

## Development Guidelines

When creating the masterplan.md, incorporate these development guidelines from the user:

### Code Quality & Design Principles

-   Follow industry-standard coding best practices (clean code, modularity, error handling, security, scalability)
-   Apply SOLID, DRY (via abstraction), and KISS principles
-   Design modular, reusable components/functions
-   Optimize for code readability and maintainable structure
-   Add concise, useful function-level comments
-   Implement comprehensive error handling (try-catch, custom errors, async handling)

### Frontend Development

-   Provide modern, clean, professional, and intuitive UI designs
-   Adhere to UI/UX principles (clarity, consistency, simplicity, feedback, accessibility/WCAG)
-   Use appropriate CSS frameworks/methodologies (e.g., Tailwind, BEM)

### React Native Guidelines (if applicable)

-   Use Expo framework with JavaScript
-   Prefer Expo Router; consider React Navigation with react-native-bottom-tabs for tabs
-   Use NativeWind, UniStyles, or Tamagui for styling
-   Use react-native-reanimated (complex) or moti (simple) for animations
-   Use TanStack Query (server), Zustand (global) for state management

### Data Handling & APIs

-   Integrate with real, live data sources and APIs as specified or implied
-   Prohibit placeholder, mock, or dummy data/API responses in the final code
-   Accept credentials/config exclusively via environment variables
-   Use `.env` files for local secrets/config with a template `.env.example` file
-   Centralize all API endpoint URLs in a single location (config file, constants module, or environment variables)
-   Never hardcode API endpoint URLs directly in service/component files

### Asset Generation

-   Do not use placeholder images or icons
-   Create necessary graphics as SVG and convert to PNG using the sharp library
-   Write build scripts to handle asset generation
-   Reference only the generated PNG files within the application code

### Documentation Requirements

-   Create a comprehensive README.md including project overview, setup instructions, and other essential information
-   Maintain a CHANGELOG.md to document changes using semantic versioning
-   Document required API keys/credentials clearly
-   Ensure all documentation is well-written, accurate, and reflects the final code

## Tool Usage Instructions

Include these instructions in the masterplan.md for the AI code assistant.

### MCP Servers and Tools

-   Use the context7 MCP server to gather contextual information about the current task, including relevant libraries, frameworks, and APIs

-   Use the clear thought MCP servers for various problem-solving approaches:

    -   `mentalmodel_clear_thought`: For applying structured problem-solving approaches (First Principles Thinking, Opportunity Cost Analysis, Error Propagation Understanding, Rubber Duck Debugging, Pareto Principle, Occam's Razor)

    -   `designpattern_clear_thought`: For applying software architecture and implementation patterns (Modular Architecture, API Integration Patterns, State Management, Asynchronous Processing, Scalability Considerations, Security Best Practices, Agentic Design Patterns)

    -   `programmingparadigm_clear_thought`: For applying different programming approaches (Imperative Programming, Procedural Programming, Object-Oriented Programming, Functional Programming, Declarative Programming, Logic Programming, Event-Driven Programming, Aspect-Oriented Programming, Concurrent Programming, Reactive Programming)

    -   `debuggingapproach_clear_thought`: For systematic debugging of technical issues (Binary Search, Reverse Engineering, Divide and Conquer, Backtracking, Cause Elimination, Program Slicing)

    -   `collaborativereasoning_clear_thought`: For simulating expert collaboration with diverse perspectives and expertise (Multi-persona problem-solving, Diverse expertise integration, Structured debate and consensus building, Perspective synthesis)

    -   `decisionframework_clear_thought`: For structured decision analysis and rational choice theory (Structured decision analysis, Multiple evaluation methodologies, Criteria weighting, Risk and uncertainty handling)

    -   `metacognitivemonitoring_clear_thought`: For tracking knowledge boundaries and reasoning quality (Metacognitive Monitoring, Knowledge boundary assessment, Claim certainty evaluation, Reasoning bias detection, Confidence calibration, Uncertainty identification)

    -   `scientificmethod_clear_thought`: For applying formal scientific reasoning to questions and problems (Structured hypothesis testing, Variable identification, Prediction formulation, Experimental design, Evidence evaluation)

    -   `structuredargumentation_clear_thought`: For dialectical reasoning and argument analysis (Thesis-antithesis-synthesis, Argument strength analysis, Premise evaluation, Logical structure mapping)

    -   `visualreasoning_clear_thought`: For visual thinking, problem-solving, and communication (Diagrammatic representation, Visual problem-solving, Spatial relationship analysis, Conceptual mapping, Visual insight generation)

    -   `sequentialthinking_clear_thought`: For breaking down complex problems into manageable steps (Structured thought process, Revision and branching support, Progress tracking, Context maintenance)

-   Use the date and time MCP server:

    -   Use `getCurrentDateTime_node` tool to get the current date and time in UTC format
    -   Add last updated date and time in UTC format to the `README.md` file

-   Use the websearch tool to find information on the internet when needed

### System & Environment Considerations

-   Target system: Windows 11 Home Single Language 23H2
-   Use semicolon (`;`) as the command separator in PowerShell commands, not `&&`
-   Use `New-Item -ItemType Directory -Path "path1", "path2", ... -Force` for creating directories in PowerShell
-   Use language-native path manipulation libraries (e.g., Node.js `path`) for robust path handling
-   Use package manager commands via the launch-process tool to add dependencies; do not edit package.json directly

### Error Handling & Debugging

-   First attempt to resolve errors autonomously using available tools
-   Perform systematic debugging: consult web resources, documentation, modify code, adjust configuration, retry
-   Report back only if an insurmountable blocker persists after exhausting all self-correction efforts

## Raw Idea Processing

When a user provides a raw idea, follow this process:

1. **Initial Processing**: Take the raw idea provided by the user and analyze it to identify ambiguities and generate relevant questions.

2. **Question Formulation**:

    - Ask all questions at once in a single response to avoid overwhelming the user with back-and-forth interactions
    - Format questions with clear options (e.g., option a, option b, etc.) whenever possible
    - For each question, provide a suggested answer that represents the most likely or optimal choice
    - Include an "Answer:" placeholder below each question so the user can easily respond without retyping the question

3. **Answer Handling**:

    - If the user leaves an answer blank, assume the suggested answer or the best possible option
    - Only ask for clarifications if absolutely necessary to proceed with generating the masterplan.md

4. **Masterplan Generation**:
    - After collecting all necessary information (either provided by the user or assumed), generate a comprehensive masterplan.md file
    - The masterplan.md should serve as a complete blueprint for the product, not just an MVP
    - Include all critical information needed for building the final product version without redundancy
    - Follow the structure outlined in the "Masterplan.md Structure" section above

## Additional Instructions

1. **Handling "Continue" Command**:

    - If the user responds with "continue" after you've asked questions, proceed with generating the masterplan.md using the suggested answers you provided
    - Treat this as an indication that the user accepts all your suggested answers and wants to move forward

2. **Feature Enhancement**:

    - Feel free to suggest additional features or requirements that would benefit the product
    - Ensure these suggestions are relevant to the core product idea and add genuine value
    - Include these suggestions in the appropriate sections of the masterplan.md

3. **Conciseness and Clarity**:

    - Create a concise masterplan.md without redundancy
    - Ensure all critical information for building the final product is included
    - Use clear, specific language that an AI code assistant can easily interpret and implement

4. **Raw Idea Placement**:
    - When the user provides a raw idea, it will be placed in the "The raw idea is:" section at the end of this file
    - Process this raw idea according to the instructions above



## The raw idea is:


java - the web site should use java, there should be a full fledged online grocery ordering system. it should have a ui website using react, it should use jdbc for database connectivity.
SNo	Feature	Story number	Description (Card)	Acceptance Criteria (Confirmation)
1	Online Grocery Ordering - 	US001	For a user, you need to develop a menu list which represents the choices.	A user can perform the action only after the successful login. Unsuccessful login will display the message that "Please Enter Correct UserName and Password". For Example: #Username: admin, #Password: admin123  Option needs to be given to the user after the successful login. Program will end only by selecting the Exit option.  1) Customer Registration (If Press 1): If user selects an option "1" then it performs "Customer Registration" operation.  2) Update Customer Details (If Press 2): If user selects an option "2" then it performs "Update Customer Details" operation.  3) Get Customer Order Details (If Press 3): If user selects an option "3" then it performs "Get Customer Order Details" operation.  4) Customer Search (If Press 4): If user selects an option "4" then it performs "Customer Search" operation.  5) Product Search (If Press 5): If user selects the option "5" then it performs "Product Search" operation.  6) Register Product (If Press 6): If user selects an option "6" then it performs "Register Product" operation.  7) Update Product (If Press 7): If user selects an option "7" then it performs "Update Product" operation.  8) Delete Product (If Press 8): If user selects an option "8" then it performs "Delete Product" operation.  9) Exit (If Press 9): If user selects the option "9" then system should print the message that "Good Bye User!". Terminating the Program".  - Invalid Option (other than 1 to 9): If Administrator is selecting the wrong option then system should print the message that "You have selected an inappropriate option. Kindly select an appropriate option.". After that system will wait for an input from the users.
2	Customer Registration	US002	As a Customer, I should be able to register myself.	Description: customer_registration() function handles the process of registering new customers for an "Online Grocery Ordering" application. It allows individuals to create an account, providing their necessary information, and subsequently, access the application's features to order groceries online. <br> Requirements: The function should prompt the user to provide the following details for registering:  1) Full Name: Customer's Full Name  2) Email: A valid email address that will serve as the username for login and communication  3) Password: A secure password that meets the application's security criteria  4) Address: Full Customer's Delivery Address  5) Contact Number: A valid phone number for communication and order updates.  Validation: - The function must validate the user input to ensure that the provided information is accurate and meets specific criteria.  - Email Address must be in the valid format and also validate that the unique email address is unique and not already registered in the system.  - The Password must meet security requirements (MinLength: 08, Presence of Uppercase, Lowercase and Special Characters)  - Phone Number must follow the exact 10 digits.  Data Storage: - Upon successful registration, store the customer's details in a database and you need to associating the information with unique customer ID (6 Digits - System Generated).  Error Handling: - Implement proper error handling mechanisms to address issues like database errors and validation failures that may arise during the registration process.
3	Update Customer Details	US003	As a Customer, I should be able to update my own details.	Description: customer_update() function handles the process of updating customer information for the "Online Grocery Ordering" application. It allows existing customers to modify their account details, such as Full Name, Email, Password, Address and Contact Number. <br> Requirements: - Before allowing any updates, the function must verify the customer's identity through proper authentication mechanism, such as requiring the customer to log in with their credentials.  User Input: - The function should prompt the user to select the type of information they want to update from a list of options. The available options might include:  - Contact Information (Full Name, Email, Address, Contact Number)  - Password  Validation: - Validate the user input to ensure that it is accurate and meets specific criteria based on selected update type.  For Example: When updating the email address, validate that the new email is unique and not already associated with another customer account.  - For Password Update, if the customer chooses to update their password, the function should enforce password complexity requirements.  Data Storage: - Upon successful validation, update the customer's details in the database, reflecting the changes made.  Error Handling: - Implement proper error handling mechanisms to address issues like database errors and validation failures that may arise during the customer update process.
4	Get Customer Order Details	US004	As a customer, I should be able to see my order history.	Description: get_customer_order_details(customer_id) function allows a customer to fetch their order history from the "Online Grocery Ordering" Application. It retrieves information such as CustomerID, Customer Name, OrderID, OrderDate, ProductID, OrderAmount. <br> Requirements: - CustomerID will be used to query the database ad retrieve the relevant information.  - Before fetching the order details, ensure that the customer is authenticated and authorized to access their own order history.  Data Retrieval: - Fetch the relevant customer information which includes CustomerID and Customer Name.  - Fetch all the orders associated with the given CustomerID and retrieve the OrderID, OrderDate, ProductID and OrderAmount.  Error Handling: - Implement proper error handling mechanisms to address issues like database errors and validation failures that may arise during the retrieval process.
5	Search customer details	US005	As a Administrator, I should be able to search customer details by customer name	Description: - As a Administrator, i am able to search the customer details by customer name.  - search_Customer_By_Name(customerName): This function allows administrator to fetch customer details just by entering customer name in the search field. <br> Requirements: - Administrator will be able to search customer details by providing any customer name.  - Before displaying search results ensure that the administrator is login through admin credentials.  (Note : Use the function ignoreCase in order to get the correct results despite of entering input in any case) <br> Data Retrieval: - Fetch the relevant customer details which includes customer_id, customer_name,email, password,address, and contact_number  - Display all the customer associated with the entered customer name.  Validations: - If entered customer name is not present in the database then throw an appropriate error message.  - Display password in encrypted format so that admin cannot access it. <br> For example: if Administrator tries to search for "Jetabtree" and if the name is not present in the database then throw an error message as "Customer not found".
6	Search product details	US006	As a customer, I should be able to search product by product name	Description: - As a customer, i am able to search the product details by product name.  - search_Product_By_Name(productName): This function allows customer to fetch product details just by entering product name in the search field. <br> Requirements: - Customers will be able to search product details by providing any particular product name.  - Before displaying search results ensure that the customer is authenticated and authorized to access the data.  (Note : Use the function ignoreCase in order to get the correct results despite of entering input in any case) <br> Data Retrieval: - Fetch the relevant product details which includes product name, product prize, quantity and add to cart button.  - Display all the product associated with the entered product name.  Validations: - If customer is unauthorized to access any details then redirect customer to registration page.  - If entered Product is not present in the database then throw an appropriate error message.  For example: If customer tries to search for "Television" and if it is not present in the database then throw an error message as "Product not found".
7	Register Product	US007	As an Admin, I should be able to register a new product.	Description: product_registration() function handles the process of registering new products for an "Online Grocery Ordering" application. It allows admin to create a product entry, providing their necessary information, and subsequently, storing them in database in Product Table. <br> Requirements: The function should prompt the user to provide the following details for registering:  1) Product ID - Numeric unique id assigned to product  2) Product Name- Product's Full Name  3) Price- Product's Price  4) Quantity- Product's Quantity  Validation: - The function must validate the user input to ensure that the provided information is accurate and meets specific criteria.  - The Quantity of product cannot be a negative number  - Product Price must be only digits.  Data Storage: - Upon successful registration, store the product's details in a database (Product Table) and you must associate the information with unique Product ID (System Generated).  Error Handling: - Implement proper error handling mechanisms to address issues like database errors and validation failures that may arise during the registration process.
8	Update Product	US008	As an Admin, I should be able to update any product.	Description: update_product() function handles the process of updating existing products for an "Online Grocery Ordering" application. It allows admin to update a product entry, providing their necessary information, and subsequently, updating them in database in Product Table. <br> Admin Input: The function should prompt the user to select the type of information they want to update from a list of options. The available options might include:  - Product ID  - Product Name  - Price  - Quantity  - Reserved  - Customer ID  Validation: - Validate the admin input to ensure that it is accurate and meets specific criteria based on selected update type.  For Example: When updating the product id, validate that the new one is unique, numeric and not already associated with another product.  Data Storage: - Upon successful validation, update the product's details in the database, reflecting the changes made.  Error Handling: - Implement proper error handling mechanisms to address issues like database errors and validation failures that may arise during the product update process.
9	Delete Product	US009	As an Admin, I should be able to delete any product.	Description: delete_product() function handles the process of deleting existing products for an "Online Grocery Ordering" application. It allows admin to delete a product entry, providing their necessary information, and subsequently, updating them in database in Product Table. <br> Admin Input: The function should prompt to select "Product ID" to be deleted.  Validation: - Validate the admin input to ensure that it is accurate and meets specific criteria based on selected update type.  For Example: When deleting the product, validate that the product id entered is existing or not.  Data Storage: - Upon successful validation, update the product's details in the database, reflecting the changes made (entry/item must be deleted).  Error Handling: - Implement proper error handling mechanisms to address issues like database errors and validation failures that may arise during the product delete process.
10	SQL Injection	US0010	prevent the SQL injection attack and code review for all user stories	This module must handle and prevent the SQL injection attack through the input data from the client/user to the application. This is important so that during code review no threat or risk occurs.  While writing the code for "Online Grocery Ordering" application, the following factors must be taken into consideration:  - There must be no unintended data entry in a program from an untrusted source.  - The input data must be utilized to dynamically create the SQL Query  For example: select id, firstname, lastname from authors <br> Input: Firstname: evil'ex and Lastname: Newman <br> Query string becomes: select id, firstname, lastname from authors where firstname = 'evil'ex' and lastname = 'newman' <br> This will result in ERROR which the database attempts to run as: Incorrect syntax near 'll' as the database tried to execute evil. <br> - The code must maintain the confidentiality of the all the users including admin.  - The code must authenticate the user everytime during login, logout and even while performing various operations that could affect the application/DB.  For example: Validating input from the user by pre-defining length, type of input, of the input field and authenticating the user. <br> - The code must take care of authorization and integrity of the data as there would be sensitive information and data.
